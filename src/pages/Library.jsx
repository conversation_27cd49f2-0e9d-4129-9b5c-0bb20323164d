import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { StudyMaterial } from "@/api/entities";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Search, Plus, BookOpen, Filter, Grid, List, Sparkles, FileText } from "lucide-react";

import MaterialCard from "../components/library/MaterialCard";
import MaterialDetail from "../components/library/MaterialDetail";

const SUBJECTS = [
  "All Subjects", "Mathematics", "Science", "History", "Literature", "Chemistry", 
  "Physics", "Biology", "Computer Science", "Psychology", "Philosophy", "Other"
];

export default function Library() {
  const [materials, setMaterials] = useState([]);
  const [filteredMaterials, setFilteredMaterials] = useState([]);
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("All Subjects");
  const [viewMode, setViewMode] = useState("grid");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadMaterials();
  }, []);

  useEffect(() => {
    filterMaterials();
  }, [materials, searchQuery, selectedSubject]);

  const loadMaterials = async () => {
    setIsLoading(true);
    try {
      const data = await StudyMaterial.list("-created_date");
      setMaterials(data);
    } catch (error) {
      console.error("Error loading materials:", error);
    }
    setIsLoading(false);
  };

  const filterMaterials = () => {
    let filtered = materials;

    if (searchQuery) {
      filtered = filtered.filter(material =>
        material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSubject !== "All Subjects") {
      filtered = filtered.filter(material => material.subject === selectedSubject);
    }

    setFilteredMaterials(filtered);
  };

  const getSubjectCounts = () => {
    const counts = {};
    materials.forEach(material => {
      const subject = material.subject || "Other";
      counts[subject] = (counts[subject] || 0) + 1;
    });
    return counts;
  };

  const subjectCounts = getSubjectCounts();

  return (
    <div className="min-h-screen p-6 md:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 mb-8"
        >
          <div>
            <h1 className="text-4xl font-bold text-gradient mb-2">Study Library</h1>
            <p className="text-slate-600 text-lg">
              {materials.length} materials • {filteredMaterials.length} displayed
            </p>
          </div>
          
          <Link to={createPageUrl("Upload")}>
            <Button 
              size="lg"
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add Materials
            </Button>
          </Link>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="glass-effect border-blue-200 rounded-2xl p-6 mb-8"
        >
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  placeholder="Search materials, content, or tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/70 border-blue-200 rounded-xl"
                />
              </div>
            </div>
            
            <Select value={selectedSubject} onValueChange={setSelectedSubject}>
              <SelectTrigger className="w-full md:w-48 bg-white/70 border-blue-200 rounded-xl">
                <SelectValue placeholder="Filter by subject" />
              </SelectTrigger>
              <SelectContent>
                {SUBJECTS.map(subject => (
                  <SelectItem key={subject} value={subject}>
                    <div className="flex items-center justify-between w-full">
                      <span>{subject}</span>
                      {subject !== "All Subjects" && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {subjectCounts[subject] || 0}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex border border-blue-200 rounded-xl overflow-hidden bg-white/70">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-none"
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-none"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Content */}
        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <Sparkles className="w-12 h-12 text-blue-500 animate-spin mx-auto mb-4" />
              <p className="text-slate-600">Loading your study materials...</p>
            </div>
          </div>
        ) : filteredMaterials.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-20"
          >
            <FileText className="w-16 h-16 text-slate-300 mx-auto mb-6" />
            <h3 className="text-2xl font-semibold text-slate-700 mb-2">
              {materials.length === 0 ? "No materials yet" : "No results found"}
            </h3>
            <p className="text-slate-500 mb-8 max-w-md mx-auto">
              {materials.length === 0 
                ? "Start building your study library by uploading your first material."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {materials.length === 0 && (
              <Link to={createPageUrl("Upload")}>
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Upload Your First Material
                </Button>
              </Link>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={
              viewMode === 'grid'
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            <AnimatePresence>
              {filteredMaterials.map((material) => (
                <MaterialCard
                  key={material.id}
                  material={material}
                  onView={setSelectedMaterial}
                />
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Material Detail Modal */}
        <AnimatePresence>
          {selectedMaterial && (
            <MaterialDetail
              material={selectedMaterial}
              onClose={() => setSelectedMaterial(null)}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}