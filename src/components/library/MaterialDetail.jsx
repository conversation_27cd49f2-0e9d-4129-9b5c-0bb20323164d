import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, FileText, Image, File, Hash, Calendar, ExternalLink, Tag } from "lucide-react";
import { format } from "date-fns";

export default function MaterialDetail({ material, onClose }) {
  const getFileIcon = (type) => {
    switch (type) {
      case 'pdf':
        return FileText;
      case 'image':
        return Image;
      default:
        return File;
    }
  };

  const FileIcon = getFileIcon(material.file_type);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        <Card className="glass-effect border-blue-200 shadow-2xl">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                  <FileIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-slate-800">{material.title}</CardTitle>
                  <div className="flex items-center gap-4 mt-2 text-sm text-slate-600">
                    <div className="flex items-center gap-1">
                      <Hash className="w-4 h-4" />
                      {material.word_count || 0} words
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {format(new Date(material.created_date), 'MMM d, yyyy')}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {material.file_url && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(material.file_url, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-1" />
                    Original
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-slate-400 hover:text-slate-600"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-8 overflow-y-auto max-h-[60vh]">
            <div className="space-y-6">
              {/* Subject and Tags */}
              <div className="flex items-center gap-6">
                {material.subject && (
                  <div>
                    <span className="text-sm font-medium text-slate-700 block mb-1">Subject</span>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {material.subject}
                    </Badge>
                  </div>
                )}
                
                {material.tags && material.tags.length > 0 && (
                  <div>
                    <span className="text-sm font-medium text-slate-700 block mb-1">Tags</span>
                    <div className="flex flex-wrap gap-1">
                      {material.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="bg-slate-50">
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Content */}
              <div>
                <h3 className="text-lg font-semibold text-slate-800 mb-4">Content</h3>
                <div className="bg-slate-50 rounded-xl p-6 border border-slate-200">
                  <pre className="whitespace-pre-wrap text-sm text-slate-700 font-sans leading-relaxed">
                    {material.content}
                  </pre>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}