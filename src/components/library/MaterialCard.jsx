import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>Header, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Image, File, Eye, Hash, Calendar, Tag } from "lucide-react";
import { format } from "date-fns";

export default function MaterialCard({ material, onView }) {
  const getFileIcon = (type) => {
    switch (type) {
      case 'pdf':
        return FileText;
      case 'image':
        return Image;
      default:
        return File;
    }
  };

  const FileIcon = getFileIcon(material.file_type);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      className="h-full"
    >
      <Card className="h-full glass-effect border-blue-200 hover:shadow-xl transition-all duration-200 cursor-pointer">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-50 rounded-xl flex items-center justify-center">
                <FileIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-slate-800 truncate">{material.title}</h3>
                <p className="text-sm text-slate-600">{material.subject || 'No subject'}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(material)}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {/* Tags */}
          {material.tags && material.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {material.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs bg-slate-50">
                  {tag}
                </Badge>
              ))}
              {material.tags.length > 3 && (
                <Badge variant="outline" className="text-xs bg-slate-50">
                  +{material.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
          
          {/* Content Preview */}
          <p className="text-sm text-slate-600 line-clamp-3 leading-relaxed">
            {material.content?.substring(0, 150)}...
          </p>
          
          {/* Stats */}
          <div className="flex items-center justify-between text-xs text-slate-500 pt-2 border-t border-slate-100">
            <div className="flex items-center gap-1">
              <Hash className="w-3 h-3" />
              {material.word_count || 0} words
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {format(new Date(material.created_date), 'MMM d')}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}