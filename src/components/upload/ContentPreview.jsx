import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Eye, Edit3, Save, BookOpen, Tag, Hash } from "lucide-react";

const SUBJECTS = [
  "Mathematics", "Science", "History", "Literature", "Chemistry", 
  "Physics", "Biology", "Computer Science", "Psychology", "Philosophy", "Other"
];

export default function ContentPreview({ extractedData, onSave, onCancel }) {
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    title: extractedData.title || extractedData.file_name || '',
    content: extractedData.content || '',
    subject: extractedData.subject || '',
    tags: extractedData.tags || [],
    ...extractedData
  });
  const [newTag, setNewTag] = useState('');

  const handleSave = () => {
    onSave({
      ...formData,
      word_count: formData.content.split(/\s+/).length,
      processing_status: 'completed'
    });
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-4xl mx-auto"
    >
      <Card className="glass-effect border-blue-200 shadow-xl">
        <CardHeader className="border-b border-blue-100">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-slate-800 flex items-center gap-3">
              <BookOpen className="w-6 h-6 text-blue-500" />
              Content Preview
            </CardTitle>
            <Button
              variant="outline"
              onClick={() => setEditMode(!editMode)}
              className="flex items-center gap-2"
            >
              {editMode ? <Eye className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
              {editMode ? 'Preview' : 'Edit'}
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="p-8">
          <div className="space-y-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Title</label>
              {editMode ? (
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter a title for this material"
                  className="text-lg font-semibold"
                />
              ) : (
                <h2 className="text-xl font-bold text-slate-800">{formData.title}</h2>
              )}
            </div>

            {/* Subject and Tags */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Subject</label>
                {editMode ? (
                  <Select
                    value={formData.subject}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, subject: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {SUBJECTS.map(subject => (
                        <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {formData.subject || 'No subject specified'}
                  </Badge>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Tags</label>
                {editMode ? (
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} variant="outline" size="sm">
                        <Tag className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map(tag => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="cursor-pointer hover:bg-red-50 hover:border-red-200"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.length > 0 ? (
                      formData.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="bg-slate-50">
                          {tag}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-slate-500 text-sm">No tags</span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-slate-700">Content</label>
                <div className="flex items-center gap-2 text-sm text-slate-500">
                  <Hash className="w-4 h-4" />
                  {formData.content.split(/\s+/).length} words
                </div>
              </div>
              
              {editMode ? (
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  className="min-h-[400px] font-mono text-sm"
                  placeholder="Extracted content will appear here..."
                />
              ) : (
                <div className="max-h-96 overflow-y-auto p-4 bg-slate-50 rounded-xl border border-slate-200">
                  <pre className="whitespace-pre-wrap text-sm text-slate-700 font-sans leading-relaxed">
                    {formData.content}
                  </pre>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-4 pt-6 border-t border-blue-100">
              <Button
                variant="outline"
                onClick={onCancel}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8"
              >
                <Save className="w-4 h-4 mr-2" />
                Save Material
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}