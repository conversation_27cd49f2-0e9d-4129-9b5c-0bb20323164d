import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { StudyMaterial } from "@/api/entities";
import { ExtractDataFromUploadedFile, UploadFile } from "@/api/integrations";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { AlertCircle, Upload as UploadIcon, Sparkles } from "lucide-react";

import FileUploadZone from "../components/upload/FileUploadZone";
import FileProcessingList from "../components/upload/FileProcessingList";
import ContentPreview from "../components/upload/ContentPreview";

export default function Upload() {
  const navigate = useNavigate();
  const [files, setFiles] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [processingStatus, setProcessingStatus] = useState([]);
  const [currentPreview, setCurrentPreview] = useState(null);
  const [error, setError] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles) => {
    const validFiles = selectedFiles.filter(file => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      const validTypes = ['application/pdf', 'text/plain', 'image/jpeg', 'image/png'];
      return file.size <= maxSize && validTypes.includes(file.type);
    });

    if (validFiles.length !== selectedFiles.length) {
      setError("Some files were skipped. Please ensure files are under 10MB and in supported formats (PDF, TXT, JPG, PNG).");
    }

    setFiles(prev => [...prev, ...validFiles]);
    setProcessingStatus(prev => [...prev, ...Array(validFiles.length).fill('pending')]);
    setError(null);
  };

  const removeFile = (index) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setProcessingStatus(prev => prev.filter((_, i) => i !== index));
  };

  const processNextFile = async () => {
    const nextFileIndex = processingStatus.findIndex(status => status === 'pending');
    if (nextFileIndex === -1) return;

    const file = files[nextFileIndex];
    setIsProcessing(true);
    
    // Update status to processing
    setProcessingStatus(prev => {
      const newStatus = [...prev];
      newStatus[nextFileIndex] = 'processing';
      return newStatus;
    });

    try {
      // Upload file first
      const { file_url } = await UploadFile({ file });
      
      // Extract content based on file type
      let extractedContent;
      
      if (file.type === 'text/plain') {
        // For text files, read directly
        const text = await file.text();
        extractedContent = {
          status: 'success',
          output: {
            content: text,
            file_type: 'txt'
          }
        };
      } else {
        // For PDF and images, use extraction service
        extractedContent = await ExtractDataFromUploadedFile({
          file_url,
          json_schema: {
            type: "object",
            properties: {
              content: { type: "string", description: "Extracted text content" },
              title: { type: "string", description: "Detected title or subject" },
              language: { type: "string", description: "Detected language" }
            }
          }
        });
      }

      if (extractedContent.status === 'success' && extractedContent.output) {
        // Update status to completed
        setProcessingStatus(prev => {
          const newStatus = [...prev];
          newStatus[nextFileIndex] = 'completed';
          return newStatus;
        });

        // Show preview
        setCurrentPreview({
          ...extractedContent.output,
          file_url,
          file_name: file.name,
          file_type: file.type.includes('pdf') ? 'pdf' : file.type.includes('image') ? 'image' : 'txt',
          title: extractedContent.output.title || file.name.replace(/\.[^/.]+$/, "")
        });
      } else {
        throw new Error('Failed to extract content');
      }
    } catch (error) {
      console.error('Processing error:', error);
      setProcessingStatus(prev => {
        const newStatus = [...prev];
        newStatus[nextFileIndex] = 'failed';
        return newStatus;
      });
      setError(`Failed to process ${file.name}. Please try again.`);
    }
    
    setIsProcessing(false);
  };

  const handleSaveMaterial = async (materialData) => {
    try {
      await StudyMaterial.create(materialData);
      
      // Remove the processed file
      const processedFileIndex = files.findIndex(f => f.name === materialData.file_name);
      if (processedFileIndex !== -1) {
        removeFile(processedFileIndex);
      }
      
      setCurrentPreview(null);
      
      // If no more files, redirect to library
      if (files.length <= 1) {
        navigate(createPageUrl("Library"));
      }
    } catch (error) {
      setError("Failed to save study material. Please try again.");
    }
  };

  const cancelPreview = () => {
    setCurrentPreview(null);
    // Remove the file that was being previewed
    const fileIndex = files.findIndex(f => f.name === currentPreview.file_name);
    if (fileIndex !== -1) {
      removeFile(fileIndex);
    }
  };

  return (
    <div className="min-h-screen p-6 md:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
            Import Study Materials
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto">
            Upload your PDFs, images, and text files. Our AI will extract and organize the content for optimal learning.
          </p>
        </motion.div>

        {/* Error Alert */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mb-6"
            >
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className="space-y-8">
          {!currentPreview && (
            <>
              {/* Upload Zone */}
              <FileUploadZone
                onFileSelect={handleFileSelect}
                dragActive={dragActive}
                setDragActive={setDragActive}
              />

              {/* File Processing List */}
              {files.length > 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="max-w-3xl mx-auto"
                >
                  <FileProcessingList
                    files={files}
                    processingStatus={processingStatus}
                    onRemoveFile={removeFile}
                  />
                  
                  {processingStatus.includes('pending') && (
                    <div className="mt-6 text-center">
                      <Button
                        onClick={processNextFile}
                        disabled={isProcessing}
                        size="lg"
                        className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl shadow-lg"
                      >
                        {isProcessing ? (
                          <>
                            <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                            Processing Content...
                          </>
                        ) : (
                          <>
                            <UploadIcon className="w-5 h-5 mr-2" />
                            Process Next File
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </motion.div>
              )}
            </>
          )}

          {/* Content Preview */}
          <AnimatePresence>
            {currentPreview && (
              <ContentPreview
                extractedData={currentPreview}
                onSave={handleSaveMaterial}
                onCancel={cancelPreview}
              />
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}