import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Upload, File, Image, FileText, Camera, Sparkles } from "lucide-react";
import { motion } from "framer-motion";

export default function FileUploadZone({ onFileSelect, dragActive, setDragActive }) {
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => {
      const validTypes = ['application/pdf', 'text/plain', 'image/jpeg', 'image/png'];
      return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB limit
    });
    
    if (validFiles.length > 0) {
      onFileSelect(validFiles);
    }
  };

  const handleFileInput = (e) => {
    const files = Array.from(e.target.files);
    onFileSelect(files);
  };

  const supportedFormats = [
    { icon: FileText, label: "PDF Documents", desc: "Research papers, textbooks" },
    { icon: File, label: "Text Files", desc: "Notes, lecture transcripts" },
    { icon: Image, label: "Images", desc: "Screenshots, photos of notes" }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative"
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.txt,.png,.jpg,.jpeg"
        onChange={handleFileInput}
        className="hidden"
      />
      
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`relative border-2 border-dashed rounded-3xl p-12 transition-all duration-200 glass-effect ${
          dragActive 
            ? "border-blue-400 bg-blue-50/50 scale-105" 
            : "border-blue-200 hover:border-blue-300"
        }`}
      >
        <div className="text-center max-w-2xl mx-auto">
          <motion.div
            animate={dragActive ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
            className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"
          >
            <Upload className="w-10 h-10 text-white" />
          </motion.div>
          
          <h3 className="text-2xl font-bold text-slate-800 mb-3">
            Import Your Study Materials
          </h3>
          <p className="text-slate-600 mb-8 text-lg">
            Drag and drop your files here, or click to browse
          </p>
          
          <Button
            onClick={() => fileInputRef.current?.click()}
            size="lg"
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 text-lg font-medium"
          >
            <Camera className="w-5 h-5 mr-2" />
            Choose Files
          </Button>
          
          <div className="grid md:grid-cols-3 gap-4 mt-12">
            {supportedFormats.map((format, index) => (
              <motion.div
                key={format.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 rounded-2xl bg-white/60 border border-blue-100 hover:bg-white/80 transition-all duration-200"
              >
                <format.icon className="w-8 h-8 text-blue-500 mx-auto mb-3" />
                <h4 className="font-semibold text-slate-800 mb-1">{format.label}</h4>
                <p className="text-sm text-slate-600">{format.desc}</p>
              </motion.div>
            ))}
          </div>
          
          <p className="text-sm text-slate-500 mt-8 flex items-center justify-center gap-2">
            <Sparkles className="w-4 h-4" />
            Maximum file size: 10MB • Supported: PDF, TXT, JPG, PNG
          </p>
        </div>
      </div>
    </motion.div>
  );
}